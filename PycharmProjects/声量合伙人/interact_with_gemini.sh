#!/bin/bash

# 与Gemini AI助手交互的脚本
# 使用方法: ./interact_with_gemini.sh "你的消息"

if [ $# -eq 0 ]; then
    echo "使用方法: $0 \"你的消息\""
    echo "示例: $0 \"你好，Gemini！\""
    exit 1
fi

MESSAGE="$1"

echo "准备向Gemini发送消息: $MESSAGE"
echo "请在Gemini终端中手动输入以下内容:"
echo "----------------------------------------"
echo "$MESSAGE"
echo "----------------------------------------"

# 尝试将消息复制到剪贴板（macOS）
if command -v pbcopy >/dev/null 2>&1; then
    echo "$MESSAGE" | pbcopy
    echo "✅ 消息已复制到剪贴板，您可以在Gemini中粘贴 (Cmd+V)"
fi

# 尝试将消息复制到剪贴板（Linux）
if command -v xclip >/dev/null 2>&1; then
    echo "$MESSAGE" | xclip -selection clipboard
    echo "✅ 消息已复制到剪贴板，您可以在Gemini中粘贴 (Ctrl+V)"
fi
